import 'package:flutter/material.dart';

class AvailabilityScreen extends StatefulWidget {
  const AvailabilityScreen({super.key});

  @override
  State<AvailabilityScreen> createState() => _AvailabilityScreenState();
}

class _AvailabilityScreenState extends State<AvailabilityScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  bool _isAvailableNow = false;
  
  // Weekly schedule
  Map<String, AvailabilityDay> _weeklySchedule = {};
  
  // Special dates (exceptions to weekly schedule)
  List<SpecialDate> _specialDates = [];
  
  // Time off requests
  List<TimeOffRequest> _timeOffRequests = [];

  final List<String> _daysOfWeek = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAvailability();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailability() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // In a real app, these would be API calls
      final availability = await _getMockAvailability();
      
      setState(() {
        _isAvailableNow = availability['is_available_now'] ?? false;
        _weeklySchedule = Map<String, AvailabilityDay>.from(
          availability['weekly_schedule'] ?? {}
        );
        _specialDates = List<SpecialDate>.from(
          availability['special_dates'] ?? []
        );
        _timeOffRequests = List<TimeOffRequest>.from(
          availability['time_off_requests'] ?? []
        );
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load availability: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<Map<String, dynamic>> _getMockAvailability() async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    return {
      'is_available_now': true,
      'weekly_schedule': {
        'Monday': AvailabilityDay(
          dayName: 'Monday',
          isAvailable: true,
          timeSlots: [
            TimeSlot(startTime: const TimeOfDay(hour: 9, minute: 0), endTime: const TimeOfDay(hour: 17, minute: 0)),
          ],
        ),
        'Tuesday': AvailabilityDay(
          dayName: 'Tuesday',
          isAvailable: true,
          timeSlots: [
            TimeSlot(startTime: const TimeOfDay(hour: 9, minute: 0), endTime: const TimeOfDay(hour: 17, minute: 0)),
          ],
        ),
        'Wednesday': AvailabilityDay(
          dayName: 'Wednesday',
          isAvailable: true,
          timeSlots: [
            TimeSlot(startTime: const TimeOfDay(hour: 10, minute: 0), endTime: const TimeOfDay(hour: 16, minute: 0)),
          ],
        ),
        'Thursday': AvailabilityDay(
          dayName: 'Thursday',
          isAvailable: true,
          timeSlots: [
            TimeSlot(startTime: const TimeOfDay(hour: 9, minute: 0), endTime: const TimeOfDay(hour: 17, minute: 0)),
          ],
        ),
        'Friday': AvailabilityDay(
          dayName: 'Friday',
          isAvailable: true,
          timeSlots: [
            TimeSlot(startTime: const TimeOfDay(hour: 9, minute: 0), endTime: const TimeOfDay(hour: 15, minute: 0)),
          ],
        ),
        'Saturday': AvailabilityDay(
          dayName: 'Saturday',
          isAvailable: true,
          timeSlots: [
            TimeSlot(startTime: const TimeOfDay(hour: 10, minute: 0), endTime: const TimeOfDay(hour: 14, minute: 0)),
          ],
        ),
        'Sunday': AvailabilityDay(
          dayName: 'Sunday',
          isAvailable: false,
          timeSlots: [],
        ),
      },
      'special_dates': [
        SpecialDate(
          date: DateTime.now().add(const Duration(days: 10)),
          type: SpecialDateType.extended,
          customSchedule: AvailabilityDay(
            dayName: 'Special',
            isAvailable: true,
            timeSlots: [
              TimeSlot(startTime: const TimeOfDay(hour: 8, minute: 0), endTime: const TimeOfDay(hour: 20, minute: 0)),
            ],
          ),
          note: 'Extended hours for holiday rush',
        ),
        SpecialDate(
          date: DateTime.now().add(const Duration(days: 15)),
          type: SpecialDateType.unavailable,
          note: 'Personal appointment',
        ),
      ],
      'time_off_requests': [
        TimeOffRequest(
          id: 1,
          startDate: DateTime.now().add(const Duration(days: 20)),
          endDate: DateTime.now().add(const Duration(days: 22)),
          reason: 'Family vacation',
          status: TimeOffStatus.approved,
          submittedDate: DateTime.now().subtract(const Duration(days: 5)),
        ),
        TimeOffRequest(
          id: 2,
          startDate: DateTime.now().add(const Duration(days: 30)),
          endDate: DateTime.now().add(const Duration(days: 31)),
          reason: 'Medical appointment',
          status: TimeOffStatus.pending,
          submittedDate: DateTime.now().subtract(const Duration(days: 2)),
        ),
      ],
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Availability'),
        backgroundColor: const Color(0xFF1B365D),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAvailability,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'Schedule'),
            Tab(text: 'Special Dates'),
            Tab(text: 'Time Off'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Quick Status Toggle
                _buildQuickStatusToggle(),
                
                // Tab Content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildScheduleTab(),
                      _buildSpecialDatesTab(),
                      _buildTimeOffTab(),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildQuickStatusToggle() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isAvailableNow ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isAvailableNow ? Colors.green.shade200 : Colors.red.shade200,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _isAvailableNow ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              _isAvailableNow ? Icons.check : Icons.close,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isAvailableNow ? 'Available Now' : 'Currently Unavailable',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _isAvailableNow ? Colors.green.shade700 : Colors.red.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _isAvailableNow 
                      ? 'You can receive new job requests'
                      : 'Turn on availability to receive jobs',
                  style: TextStyle(
                    fontSize: 14,
                    color: _isAvailableNow ? Colors.green.shade600 : Colors.red.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isAvailableNow,
            onChanged: _toggleAvailabilityNow,
            activeColor: Colors.green,
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text(
                  'Weekly Schedule',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B365D),
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _copyPreviousWeek,
                  icon: const Icon(Icons.copy),
                  label: const Text('Copy Last Week'),
                ),
              ],
            ),
          ),
          ..._daysOfWeek.map((day) => _buildDayScheduleCard(day)),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(
              onPressed: _saveSchedule,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1B365D),
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 48),
              ),
              child: const Text('Save Schedule'),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDayScheduleCard(String day) {
    final daySchedule = _weeklySchedule[day] ?? AvailabilityDay(
      dayName: day,
      isAvailable: false,
      timeSlots: [],
    );

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  day,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1B365D),
                  ),
                ),
                const Spacer(),
                Switch(
                  value: daySchedule.isAvailable,
                  onChanged: (value) => _toggleDayAvailability(day, value),
                  activeColor: const Color(0xFF1B365D),
                ),
              ],
            ),
          ),
          if (daySchedule.isAvailable) ...[
            ...daySchedule.timeSlots.map((slot) => _buildTimeSlotItem(day, slot)),
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextButton.icon(
                onPressed: () => _addTimeSlot(day),
                icon: const Icon(Icons.add),
                label: const Text('Add Time Slot'),
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFF1B365D),
                ),
              ),
            ),
          ] else ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Not available on $day',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimeSlotItem(String day, TimeSlot slot) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          const SizedBox(width: 32),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF1B365D).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '${_formatTimeOfDay(slot.startTime)} - ${_formatTimeOfDay(slot.endTime)}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1B365D),
              ),
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => _editTimeSlot(day, slot),
            icon: const Icon(Icons.edit, size: 20),
            style: IconButton.styleFrom(padding: const EdgeInsets.all(8)),
          ),
          IconButton(
            onPressed: () => _removeTimeSlot(day, slot),
            icon: const Icon(Icons.delete, size: 20),
            color: Colors.red,
            style: IconButton.styleFrom(padding: const EdgeInsets.all(8)),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialDatesTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Special Dates & Exceptions',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B365D),
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _addSpecialDate,
                icon: const Icon(Icons.add),
                label: const Text('Add Date'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1B365D),
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: _specialDates.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 64,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No Special Dates',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B365D),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Add exceptions to your regular schedule',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _specialDates.length,
                  itemBuilder: (context, index) {
                    final specialDate = _specialDates[index];
                    return _buildSpecialDateCard(specialDate);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSpecialDateCard(SpecialDate specialDate) {
    Color typeColor;
    IconData typeIcon;
    String typeText;

    switch (specialDate.type) {
      case SpecialDateType.extended:
        typeColor = Colors.green;
        typeIcon = Icons.access_time;
        typeText = 'Extended Hours';
        break;
      case SpecialDateType.reduced:
        typeColor = Colors.orange;
        typeIcon = Icons.schedule;
        typeText = 'Reduced Hours';
        break;
      case SpecialDateType.unavailable:
        typeColor = Colors.red;
        typeIcon = Icons.cancel;
        typeText = 'Unavailable';
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: typeColor.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: typeColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(typeIcon, color: typeColor, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _formatDate(specialDate.date),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1B365D),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: typeColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        typeText,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: typeColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleSpecialDateAction(value, specialDate),
                itemBuilder: (context) => [
                  const PopupMenuItem(value: 'edit', child: Text('Edit')),
                  const PopupMenuItem(value: 'delete', child: Text('Delete')),
                ],
              ),
            ],
          ),
          if (specialDate.customSchedule != null && specialDate.customSchedule!.isAvailable) ...[
            const SizedBox(height: 12),
            ...specialDate.customSchedule!.timeSlots.map((slot) => Padding(
                  padding: const EdgeInsets.only(left: 40),
                  child: Text(
                    '${_formatTimeOfDay(slot.startTime)} - ${_formatTimeOfDay(slot.endTime)}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                )),
          ],
          if (specialDate.note != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.note, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      specialDate.note!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimeOffTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Text(
                'Time Off Requests',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B365D),
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _requestTimeOff,
                icon: const Icon(Icons.add),
                label: const Text('Request Time Off'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1B365D),
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: _timeOffRequests.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.beach_access_outlined,
                        size: 64,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No Time Off Requests',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1B365D),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Request time off for vacations or personal time',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _timeOffRequests.length,
                  itemBuilder: (context, index) {
                    final request = _timeOffRequests[index];
                    return _buildTimeOffCard(request);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildTimeOffCard(TimeOffRequest request) {
    Color statusColor;
    IconData statusIcon;

    switch (request.status) {
      case TimeOffStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        break;
      case TimeOffStatus.approved:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case TimeOffStatus.denied:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(statusIcon, color: statusColor, size: 20),
              const SizedBox(width: 8),
              Text(
                '${_formatDate(request.startDate)} - ${_formatDate(request.endDate)}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1B365D),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  request.status.toString().split('.').last.toUpperCase(),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Reason: ${request.reason}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Submitted: ${_formatDate(request.submittedDate)}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  void _toggleAvailabilityNow(bool value) {
    setState(() {
      _isAvailableNow = value;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(value ? 'You are now available for jobs' : 'You are now unavailable'),
        backgroundColor: value ? Colors.green : Colors.orange,
      ),
    );
  }

  void _toggleDayAvailability(String day, bool value) {
    setState(() {
      _weeklySchedule[day] = AvailabilityDay(
        dayName: day,
        isAvailable: value,
        timeSlots: value ? [
          TimeSlot(
            startTime: const TimeOfDay(hour: 9, minute: 0),
            endTime: const TimeOfDay(hour: 17, minute: 0),
          ),
        ] : [],
      );
    });
  }

  void _addTimeSlot(String day) {
    showDialog(
      context: context,
      builder: (context) => _TimeSlotDialog(
        title: 'Add Time Slot',
        onSave: (startTime, endTime) {
          setState(() {
            _weeklySchedule[day]?.timeSlots.add(
              TimeSlot(startTime: startTime, endTime: endTime),
            );
          });
        },
      ),
    );
  }

  void _editTimeSlot(String day, TimeSlot slot) {
    showDialog(
      context: context,
      builder: (context) => _TimeSlotDialog(
        title: 'Edit Time Slot',
        initialStartTime: slot.startTime,
        initialEndTime: slot.endTime,
        onSave: (startTime, endTime) {
          setState(() {
            final index = _weeklySchedule[day]?.timeSlots.indexOf(slot) ?? -1;
            if (index >= 0) {
              _weeklySchedule[day]?.timeSlots[index] = TimeSlot(
                startTime: startTime,
                endTime: endTime,
              );
            }
          });
        },
      ),
    );
  }

  void _removeTimeSlot(String day, TimeSlot slot) {
    setState(() {
      _weeklySchedule[day]?.timeSlots.remove(slot);
    });
  }

  void _copyPreviousWeek() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Previous week schedule copied')),
    );
  }

  void _saveSchedule() async {
    try {
      // In real app, save to API
      await Future.delayed(const Duration(seconds: 1));
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Schedule saved successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to save schedule: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _addSpecialDate() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add special date feature coming soon')),
    );
  }

  void _handleSpecialDateAction(String action, SpecialDate specialDate) {
    switch (action) {
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Edit special date feature coming soon')),
        );
        break;
      case 'delete':
        setState(() {
          _specialDates.remove(specialDate);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Special date deleted')),
        );
        break;
    }
  }

  void _requestTimeOff() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Time off request feature coming soon')),
    );
  }
}

class _TimeSlotDialog extends StatefulWidget {
  final String title;
  final TimeOfDay? initialStartTime;
  final TimeOfDay? initialEndTime;
  final Function(TimeOfDay startTime, TimeOfDay endTime) onSave;

  const _TimeSlotDialog({
    required this.title,
    this.initialStartTime,
    this.initialEndTime,
    required this.onSave,
  });

  @override
  State<_TimeSlotDialog> createState() => _TimeSlotDialogState();
}

class _TimeSlotDialogState extends State<_TimeSlotDialog> {
  late TimeOfDay _startTime;
  late TimeOfDay _endTime;

  @override
  void initState() {
    super.initState();
    _startTime = widget.initialStartTime ?? const TimeOfDay(hour: 9, minute: 0);
    _endTime = widget.initialEndTime ?? const TimeOfDay(hour: 17, minute: 0);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.schedule),
            title: const Text('Start Time'),
            subtitle: Text(_formatTimeOfDay(_startTime)),
            onTap: () async {
              final time = await showTimePicker(
                context: context,
                initialTime: _startTime,
              );
              if (time != null) {
                setState(() {
                  _startTime = time;
                });
              }
            },
          ),
          ListTile(
            leading: const Icon(Icons.schedule),
            title: const Text('End Time'),
            subtitle: Text(_formatTimeOfDay(_endTime)),
            onTap: () async {
              final time = await showTimePicker(
                context: context,
                initialTime: _endTime,
              );
              if (time != null) {
                setState(() {
                  _endTime = time;
                });
              }
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_isValidTimeSlot()) {
              widget.onSave(_startTime, _endTime);
              Navigator.of(context).pop();
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('End time must be after start time'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period = time.period == DayPeriod.am ? 'AM' : 'PM';
    return '$hour:$minute $period';
  }

  bool _isValidTimeSlot() {
    final startMinutes = _startTime.hour * 60 + _startTime.minute;
    final endMinutes = _endTime.hour * 60 + _endTime.minute;
    return endMinutes > startMinutes;
  }
}

// Data Models
class AvailabilityDay {
  final String dayName;
  final bool isAvailable;
  final List<TimeSlot> timeSlots;

  AvailabilityDay({
    required this.dayName,
    required this.isAvailable,
    required this.timeSlots,
  });
}

class TimeSlot {
  final TimeOfDay startTime;
  final TimeOfDay endTime;

  TimeSlot({
    required this.startTime,
    required this.endTime,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeSlot &&
        other.startTime == startTime &&
        other.endTime == endTime;
  }

  @override
  int get hashCode => startTime.hashCode ^ endTime.hashCode;
}

class SpecialDate {
  final DateTime date;
  final SpecialDateType type;
  final AvailabilityDay? customSchedule;
  final String? note;

  SpecialDate({
    required this.date,
    required this.type,
    this.customSchedule,
    this.note,
  });
}

enum SpecialDateType {
  extended,
  reduced,
  unavailable,
}

class TimeOffRequest {
  final int id;
  final DateTime startDate;
  final DateTime endDate;
  final String reason;
  final TimeOffStatus status;
  final DateTime submittedDate;

  TimeOffRequest({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.reason,
    required this.status,
    required this.submittedDate,
  });
}

enum TimeOffStatus {
  pending,
  approved,
  denied,
}