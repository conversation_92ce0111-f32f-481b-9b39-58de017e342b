import 'package:flutter/material.dart';
import '../../../models/errand.dart';
import '../../../services/runner_service.dart';
import '../../../widgets/runner/errand_card.dart';

class ErrandsScreen extends StatefulWidget {
  const ErrandsScreen({super.key});

  @override
  State<ErrandsScreen> createState() => _ErrandsScreenState();
}

class _ErrandsScreenState extends State<ErrandsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Errand> _availableErrands = [];
  List<Errand> _acceptedErrands = [];
  List<Errand> _completedErrands = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadErrands();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadErrands() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final availableErrands = await RunnerService.getAvailableErrands();
      final acceptedErrands = await RunnerService.getMyErrands();
      
      // Filter completed errands from accepted ones
      final completedErrands = acceptedErrands.where((errand) => 
        errand.status == 'completed'
      ).toList();
      
      final activeErrands = acceptedErrands.where((errand) => 
        errand.status != 'completed' && errand.status != 'cancelled'
      ).toList();

      setState(() {
        _availableErrands = availableErrands;
        _acceptedErrands = activeErrands;
        _completedErrands = completedErrands;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load errands: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _acceptErrand(String errandId) async {
    // Check if user already has active tasks
    if (_acceptedErrands.isNotEmpty) {
      final hasActiveTasks = _acceptedErrands.any((errand) => 
        errand.statusEnum == ErrandStatus.accepted || 
        errand.statusEnum == ErrandStatus.inProgress
      );
      
      if (hasActiveTasks) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Active Task Detected'),
            content: const Text('You already have an active task. Please complete it before accepting a new one.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Navigate to active tasks screen
                  context.push('/runner/active-errands');
                },
                child: const Text('View Active Tasks'),
              ),
            ],
          ),
        );
        return;
      }
    }

    try {
      await RunnerService.acceptErrand(errandId);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Errand accepted successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      _loadErrands(); // Refresh data
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to accept errand: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Errands'),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Available'),
                  if (_availableErrands.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        _availableErrands.length.toString(),
                        style: const TextStyle(
                          color: Color(0xFF1B365D),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Active'),
                  if (_acceptedErrands.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        _acceptedErrands.length.toString(),
                        style: const TextStyle(
                          color: Color(0xFF1B365D),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const Tab(text: 'Completed'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Available Errands
          RefreshIndicator(
            onRefresh: _loadErrands,
            child: _buildErrandsList(
              errands: _availableErrands,
              isLoading: _isLoading,
              emptyMessage: 'No available errands right now',
              emptySubtitle: 'Check back later for new opportunities',
              showAcceptButton: true,
            ),
          ),
          
          // Active Errands
          RefreshIndicator(
            onRefresh: _loadErrands,
            child: _buildErrandsList(
              errands: _acceptedErrands,
              isLoading: _isLoading,
              emptyMessage: 'No active errands',
              emptySubtitle: 'Accept errands from the Available tab to see them here',
              showAcceptButton: false,
            ),
          ),
          
          // Completed Errands
          RefreshIndicator(
            onRefresh: _loadErrands,
            child: _buildErrandsList(
              errands: _completedErrands,
              isLoading: _isLoading,
              emptyMessage: 'No completed errands yet',
              emptySubtitle: 'Your completed errands will appear here',
              showAcceptButton: false,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrandsList({
    required List<Errand> errands,
    required bool isLoading,
    required String emptyMessage,
    required String emptySubtitle,
    required bool showAcceptButton,
  }) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (errands.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.work_outline,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                emptyMessage,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                emptySubtitle,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: _loadErrands,
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1B365D),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: errands.length,
      itemBuilder: (context, index) {
        final errand = errands[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: showAcceptButton
              ? ErrandCard(
                  errand: errand,
                  onAccept: () => _acceptErrand(errand.id),
                )
              : _buildActiveErrandCard(errand),
        );
      },
    );
  }

  Widget _buildActiveErrandCard(Errand errand) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status indicator
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: _getStatusColor(errand.statusEnum).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _getStatusText(errand.statusEnum),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: _getStatusColor(errand.statusEnum),
                ),
              ),
            ),
            const SizedBox(height: 12),
            
            // Header with title and payment
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    errand.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1B365D),
                    ),
                  ),
                ),
                Text(
                  '\$${errand.payment.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            
            // Description
            Text(
              errand.description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                height: 1.4,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 16),
            
            // Action buttons
            if (errand.status == ErrandStatus.accepted)
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        // TODO: Start errand
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Start errand feature coming soon!'),
                          ),
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Color(0xFF1B365D)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Start',
                        style: TextStyle(color: Color(0xFF1B365D)),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // TODO: Contact customer
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Contact feature coming soon!'),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1B365D),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Contact',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(ErrandStatus status) {
    switch (status) {
      case ErrandStatus.pending:
        return Colors.grey;
      case ErrandStatus.requested:
        return Colors.blue;
      case ErrandStatus.accepted:
        return Colors.orange;
      case ErrandStatus.inProgress:
        return Colors.purple;
      case ErrandStatus.completed:
        return Colors.green;
      case ErrandStatus.cancelled:
        return Colors.red;
    }
  }

  String _getStatusText(ErrandStatus status) {
    switch (status) {
      case ErrandStatus.pending:
        return 'Pending';
      case ErrandStatus.requested:
        return 'Available';
      case ErrandStatus.accepted:
        return 'Accepted';
      case ErrandStatus.inProgress:
        return 'In Progress';
      case ErrandStatus.completed:
        return 'Completed';
      case ErrandStatus.cancelled:
        return 'Cancelled';
    }
  }
}