import 'user.dart';

class Errand {
  final String id;
  final String serviceCategory;
  final String title;
  final String description;
  final String pickupLocation;
  final String deliveryLocation;
  final DateTime scheduledTime;
  final String priority;
  final double budget;
  final String status;
  final String userId;
  final String? runnerId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User? runner;

  Errand({
    required this.id,
    required this.serviceCategory,
    required this.title,
    required this.description,
    required this.pickupLocation,
    required this.deliveryLocation,
    required this.scheduledTime,
    required this.priority,
    required this.budget,
    required this.status,
    required this.userId,
    this.runnerId,
    required this.createdAt,
    required this.updatedAt,
    this.runner,
  });

  // Backward compatibility getters
  String get serviceName => title;
  DateTime get scheduledDate => scheduledTime;
  String get location => pickupLocation.isNotEmpty ? pickupLocation : deliveryLocation;
  double get totalAmount => budget;
  double get payment => budget;
  String? get runnerName => runner?.fullName;
  String? get runnerPhone => runner?.phone;
  String get notes => description;

  // Helper getter to convert string status to ErrandStatus
  ErrandStatus get statusEnum {
    return ErrandStatus.values.firstWhere(
      (e) => e.toString().split('.').last == status,
      orElse: () => ErrandStatus.pending,
    );
  }

  factory Errand.fromJson(Map<String, dynamic> json) {
    return Errand(
      id: json['id'],
      serviceCategory: json['service_category'] ?? '',
      title: json['title'],
      description: json['description'],
      pickupLocation: json['pickup_location'] ?? '',
      deliveryLocation: json['delivery_location'] ?? '',
      scheduledTime: DateTime.parse(json['scheduled_time']),
      priority: json['priority'] ?? 'normal',
      budget: double.tryParse(json['budget'].toString()) ?? 0.0,
      status: json['status'] ?? 'pending',
      userId: json['user_id'],
      runnerId: json['runner_id'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      runner: json['runner'] != null ? User.fromJson(json['runner']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'service_category': serviceCategory,
      'title': title,
      'description': description,
      'pickup_location': pickupLocation,
      'delivery_location': deliveryLocation,
      'scheduled_time': scheduledTime.toIso8601String(),
      'priority': priority,
      'budget': budget,
      'status': status,
      'user_id': userId,
      'runner_id': runnerId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'runner': runner?.toJson(),
    };
  }

  String get formattedBudget => '\$${budget.toStringAsFixed(2)}';
  
  ErrandStatus get errandStatus {
    switch (status.toLowerCase()) {
      case 'pending':
        return ErrandStatus.pending;
      case 'accepted':
        return ErrandStatus.accepted;
      case 'in_progress':
        return ErrandStatus.inProgress;
      case 'completed':
        return ErrandStatus.completed;
      case 'cancelled':
        return ErrandStatus.cancelled;
      default:
        return ErrandStatus.pending;
    }
  }

  String get priorityLabel {
    switch (priority.toLowerCase()) {
      case 'urgent':
        return 'Urgent';
      case 'high':
        return 'High Priority';
      case 'normal':
        return 'Normal';
      case 'low':
        return 'Low Priority';
      default:
        return 'Normal';
    }
  }

  String get categoryLabel {
    switch (serviceCategory.toLowerCase()) {
      case 'delivery':
        return 'Delivery';
      case 'shopping':
        return 'Shopping';
      case 'food':
        return 'Food Delivery';
      case 'pharmacy':
        return 'Pharmacy';
      case 'general':
        return 'General Errands';
      default:
        return serviceCategory;
    }
  }
}

enum ErrandStatus {
  pending,
  requested,
  accepted,
  inProgress,
  completed,
  cancelled,
}

enum EarningType {
  job,
  tip,
  bonus,
}

class RunnerEarning {
  final String id;
  final double amount;
  final String description;
  final DateTime date;
  final String? type;

  RunnerEarning({
    required this.id,
    required this.amount,
    required this.description,
    required this.date,
    this.type,
  });

  // Helper getter to convert string type to EarningType
  EarningType get earningType {
    return EarningType.values.firstWhere(
      (e) => e.toString().split('.').last == type,
      orElse: () => EarningType.job,
    );
  }

  factory RunnerEarning.fromJson(Map<String, dynamic> json) {
    return RunnerEarning(
      id: json['id'].toString(),
      amount: double.tryParse(json['amount'].toString()) ?? 0.0,
      description: json['description'],
      date: DateTime.parse(json['date']),
      type: json['type'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'description': description,
      'date': date.toIso8601String(),
      'type': type,
    };
  }

  String get formattedAmount => '\$${amount.toStringAsFixed(2)}';
}

class WithdrawRequest {
  final double amount;
  final String method;

  WithdrawRequest({
    required this.amount,
    required this.method,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount.toStringAsFixed(2),
      'method': method,
    };
  }
}

class ErrandListResponse {
  final List<Errand> data;
  final ErrandMeta? meta;

  ErrandListResponse({
    required this.data,
    this.meta,
  });

  factory ErrandListResponse.fromJson(Map<String, dynamic> json) {
    return ErrandListResponse(
      data: (json['data'] as List)
          .map((e) => Errand.fromJson(e))
          .toList(),
      meta: json['meta'] != null ? ErrandMeta.fromJson(json['meta']) : null,
    );
  }
}

class ErrandMeta {
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;

  ErrandMeta({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
  });

  factory ErrandMeta.fromJson(Map<String, dynamic> json) {
    return ErrandMeta(
      currentPage: json['current_page'],
      lastPage: json['last_page'],
      perPage: json['per_page'],
      total: json['total'],
    );
  }
}