class User {
  final String id;
  final String firstName;
  final String lastName;
  final String fullName;
  final String email;
  final String? phone;
  final UserRole role;
  final String status;
  final bool emailVerified;
  final bool phoneVerified;
  final bool fullyVerified;
  final String? profilePicture;
  final AgentProfile? agentProfile;
  final DateTime createdAt;
  final DateTime? updatedAt;

  User({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.fullName,
    required this.email,
    this.phone,
    required this.role,
    required this.status,
    required this.emailVerified,
    required this.phoneVerified,
    required this.fullyVerified,
    this.profilePicture,
    this.agentProfile,
    required this.createdAt,
    this.updatedAt,
  });

  // Backward compatibility getters
  String get name => fullName;
  String? get phoneNumber => phone;

  factory User.fromJson(Map<String, dynamic> json) {
    try {
      // Based on actual API response structure
      final firstName = json['first_name'] ?? '';
      final lastName = json['last_name'] ?? '';
      final fullName = json['full_name'] ?? '$firstName $lastName';
      
      return User(
        id: json['id']?.toString() ?? '',
        firstName: firstName,
        lastName: lastName,
        fullName: fullName.isNotEmpty ? fullName : '$firstName $lastName',
        email: json['email'] ?? '',
        phone: json['phone'],
        role: UserRole.values.firstWhere(
          (role) => role.toString().split('.').last == json['role'],
          orElse: () => UserRole.client,
        ),
        status: json['status'] ?? 'active',
        emailVerified: json['email_verified_at'] != null,
        phoneVerified: json['phone_verified_at'] != null,
        fullyVerified: (json['email_verified_at'] != null) && (json['phone_verified_at'] != null),
        profilePicture: json['profile_picture'],
        agentProfile: json['agent_profile'] != null 
            ? AgentProfile.fromJson(json['agent_profile'])
            : null,
        createdAt: json['created_at'] != null 
            ? DateTime.parse(json['created_at'])
            : DateTime.now(),
        updatedAt: json['updated_at'] != null 
            ? DateTime.parse(json['updated_at'])
            : null,
      );
    } catch (e) {
      print('Error parsing User from JSON: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'role': role.toString().split('.').last,
      'status': status,
      'email_verified': emailVerified,
      'phone_verified': phoneVerified,
      'fully_verified': fullyVerified,
      'profile_picture': profilePicture,
      'agent_profile': agentProfile?.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  User copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? fullName,
    String? email,
    String? phone,
    UserRole? role,
    String? status,
    bool? emailVerified,
    bool? phoneVerified,
    bool? fullyVerified,
    String? profilePicture,
    AgentProfile? agentProfile,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      status: status ?? this.status,
      emailVerified: emailVerified ?? this.emailVerified,
      phoneVerified: phoneVerified ?? this.phoneVerified,
      fullyVerified: fullyVerified ?? this.fullyVerified,
      profilePicture: profilePicture ?? this.profilePicture,
      agentProfile: agentProfile ?? this.agentProfile,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName => fullName.isNotEmpty ? fullName : '$firstName $lastName';
}

class AgentProfile {
  final String id;
  final String verificationStatus;
  final double ratingAverage;
  final int totalJobsCompleted;
  final bool isAvailable;

  AgentProfile({
    required this.id,
    required this.verificationStatus,
    required this.ratingAverage,
    required this.totalJobsCompleted,
    required this.isAvailable,
  });

  factory AgentProfile.fromJson(Map<String, dynamic> json) {
    return AgentProfile(
      id: json['id'],
      verificationStatus: json['verification_status'],
      ratingAverage: double.tryParse(json['rating_average'].toString()) ?? 0.0,
      totalJobsCompleted: json['total_jobs_completed'] ?? 0,
      isAvailable: json['is_available'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'verification_status': verificationStatus,
      'rating_average': ratingAverage,
      'total_jobs_completed': totalJobsCompleted,
      'is_available': isAvailable,
    };
  }
}

enum UserRole {
  client,
  agent,
  admin,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.client:
        return 'Customer';
      case UserRole.agent:
        return 'Runner';
      case UserRole.admin:
        return 'Admin';
    }
  }
}