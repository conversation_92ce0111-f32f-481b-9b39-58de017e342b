import 'user.dart';

class AuthResponse {
  final bool success;
  final String message;
  final AuthData? data;

  AuthResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? AuthData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

class AuthData {
  final User user;
  final String token;
  final String tokenType;
  final bool requiresVerification;

  AuthData({
    required this.user,
    required this.token,
    required this.tokenType,
    required this.requiresVerification,
  });

  factory AuthData.fromJson(Map<String, dynamic> json) {
    return AuthData(
      user: User.from<PERSON><PERSON>(json['user']),
      token: json['token'],
      tokenType: json['token_type'] ?? 'Bearer',
      requiresVerification: json['requires_verification'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'token': token,
      'token_type': tokenType,
      'requires_verification': requiresVerification,
    };
  }
}

class LoginRequest {
  final String email;
  final String password;

  LoginRequest({
    required this.email,
    required this.password,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }
}

class RegisterRequest {
  final String name;
  final String email;
  final String password;
  final String passwordConfirmation;
  final UserRole role;
  final String? phoneNumber;

  RegisterRequest({
    required this.name,
    required this.email,
    required this.password,
    required this.passwordConfirmation,
    required this.role,
    this.phoneNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'password': password,
      'password_confirmation': passwordConfirmation,
      'role': role.toString().split('.').last,
      if (phoneNumber != null) 'phone_number': phoneNumber,
    };
  }
}

class ForgotPasswordRequest {
  final String email;

  ForgotPasswordRequest({required this.email});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }
}

class UpdateProfileRequest {
  final String? name;
  final String? phoneNumber;

  UpdateProfileRequest({
    this.name,
    this.phoneNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      if (name != null) 'name': name,
      if (phoneNumber != null) 'phone_number': phoneNumber,
    };
  }
}